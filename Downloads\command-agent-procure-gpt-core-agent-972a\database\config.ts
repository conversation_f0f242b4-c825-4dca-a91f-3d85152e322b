/**
 * Database Configuration for LangGraph Agent System
 * PostgreSQL 18 + pgvector + Redis Configuration
 */

import { Pool, PoolConfig } from 'pg';
import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

// ============================================================================
// POSTGRESQL CONFIGURATION
// ============================================================================

export interface DatabaseConfig extends PoolConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean | object;
  max?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

export const databaseConfig: DatabaseConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DB || 'langgraph_agent_db',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'password',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: parseInt(process.env.POSTGRES_MAX_CONNECTIONS || '20'),
  idleTimeoutMillis: parseInt(process.env.POSTGRES_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: parseInt(process.env.POSTGRES_CONNECTION_TIMEOUT || '10000'),
};

// Connection pool instance
let pool: Pool | null = null;

export function getPool(): Pool {
  if (!pool) {
    pool = new Pool(databaseConfig);
    
    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
      process.exit(-1);
    });

    // Handle pool connection events
    pool.on('connect', (client) => {
      console.log('New client connected to PostgreSQL');
    });

    pool.on('remove', (client) => {
      console.log('Client removed from PostgreSQL pool');
    });
  }
  
  return pool;
}

// Test database connection
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const pool = getPool();
    const client = await pool.connect();
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('Database connection successful:', result.rows[0]);
    
    // Test pgvector extension
    const vectorTest = await client.query('SELECT 1 as test WHERE EXISTS (SELECT 1 FROM pg_extension WHERE extname = $1)', ['vector']);
    if (vectorTest.rows.length === 0) {
      console.warn('pgvector extension not found. Please install it: CREATE EXTENSION vector;');
    } else {
      console.log('pgvector extension is available');
    }
    
    client.release();
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

// ============================================================================
// REDIS CONFIGURATION
// ============================================================================

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  keyPrefix?: string;
}

export const redisConfig: RedisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'langgraph:',
};

// Redis instances
let redisClient: Redis | null = null;
let redisSubscriber: Redis | null = null;
let redisPublisher: Redis | null = null;

export function getRedisClient(): Redis {
  if (!redisClient) {
    redisClient = new Redis(redisConfig);
    
    redisClient.on('connect', () => {
      console.log('Redis client connected');
    });
    
    redisClient.on('error', (err) => {
      console.error('Redis client error:', err);
    });
    
    redisClient.on('ready', () => {
      console.log('Redis client ready');
    });
  }
  
  return redisClient;
}

export function getRedisSubscriber(): Redis {
  if (!redisSubscriber) {
    redisSubscriber = new Redis(redisConfig);
    
    redisSubscriber.on('connect', () => {
      console.log('Redis subscriber connected');
    });
    
    redisSubscriber.on('error', (err) => {
      console.error('Redis subscriber error:', err);
    });
  }
  
  return redisSubscriber;
}

export function getRedisPublisher(): Redis {
  if (!redisPublisher) {
    redisPublisher = new Redis(redisConfig);
    
    redisPublisher.on('connect', () => {
      console.log('Redis publisher connected');
    });
    
    redisPublisher.on('error', (err) => {
      console.error('Redis publisher error:', err);
    });
  }
  
  return redisPublisher;
}

// Test Redis connection
export async function testRedisConnection(): Promise<boolean> {
  try {
    const client = getRedisClient();
    await client.ping();
    console.log('Redis connection successful');
    
    // Test basic operations
    await client.set('test:connection', 'success', 'EX', 10);
    const result = await client.get('test:connection');
    console.log('Redis test operation result:', result);
    
    return true;
  } catch (error) {
    console.error('Redis connection failed:', error);
    return false;
  }
}

// ============================================================================
// DATABASE UTILITIES
// ============================================================================

export class DatabaseManager {
  private pool: Pool;
  private redis: Redis;

  constructor() {
    this.pool = getPool();
    this.redis = getRedisClient();
  }

  // Execute query with connection pooling
  async query(text: string, params?: any[]): Promise<any> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  // Execute transaction
  async transaction<T>(callback: (client: any) => Promise<T>): Promise<T> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Cache operations
  async setCache(key: string, value: any, ttl: number = 3600): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  async getCache<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async deleteCache(key: string): Promise<void> {
    await this.redis.del(key);
  }

  async deleteCachePattern(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }

  // Health check
  async healthCheck(): Promise<{ postgres: boolean; redis: boolean }> {
    const postgres = await testDatabaseConnection();
    const redis = await testRedisConnection();
    
    return { postgres, redis };
  }

  // Graceful shutdown
  async close(): Promise<void> {
    try {
      if (pool) {
        await pool.end();
        console.log('PostgreSQL pool closed');
      }
      
      if (redisClient) {
        redisClient.disconnect();
        console.log('Redis client disconnected');
      }
      
      if (redisSubscriber) {
        redisSubscriber.disconnect();
        console.log('Redis subscriber disconnected');
      }
      
      if (redisPublisher) {
        redisPublisher.disconnect();
        console.log('Redis publisher disconnected');
      }
    } catch (error) {
      console.error('Error during database cleanup:', error);
    }
  }
}

// Singleton instance
let dbManager: DatabaseManager | null = null;

export function getDatabaseManager(): DatabaseManager {
  if (!dbManager) {
    dbManager = new DatabaseManager();
  }
  return dbManager;
}

// ============================================================================
// INITIALIZATION AND SETUP
// ============================================================================

export async function initializeDatabase(): Promise<boolean> {
  try {
    console.log('Initializing database connections...');
    
    // Test connections
    const postgresOk = await testDatabaseConnection();
    const redisOk = await testRedisConnection();
    
    if (!postgresOk) {
      throw new Error('PostgreSQL connection failed');
    }
    
    if (!redisOk) {
      console.warn('Redis connection failed - some features may be limited');
    }
    
    // Run any initialization queries if needed
    const pool = getPool();
    const client = await pool.connect();
    
    try {
      // Ensure extensions are loaded
      await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
      await client.query('CREATE EXTENSION IF NOT EXISTS vector');
      
      console.log('Database initialization completed successfully');
      return true;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Database initialization failed:', error);
    return false;
  }
}

// Graceful shutdown handler
process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing database connections...');
  const dbManager = getDatabaseManager();
  await dbManager.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing database connections...');
  const dbManager = getDatabaseManager();
  await dbManager.close();
  process.exit(0);
});

export default {
  databaseConfig,
  redisConfig,
  getPool,
  getRedisClient,
  getRedisSubscriber,
  getRedisPublisher,
  getDatabaseManager,
  initializeDatabase,
  testDatabaseConnection,
  testRedisConnection,
};
