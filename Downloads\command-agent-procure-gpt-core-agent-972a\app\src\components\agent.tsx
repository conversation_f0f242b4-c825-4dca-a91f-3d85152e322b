"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import ReactMarkdown from 'react-markdown';
import { VendorDashboard } from "@/components/vendors/VendorDashboard";
import {
  Send,
  Zap,
  TrendingUp,
  Shield,
  DollarSign,
  Clock,
  AlertTriangle,
  Play,
  Pause,
  RotateCcw,
  Users,
  FileText,
  CheckCircle,
  XCircle,
  Eye,
  Building
} from "lucide-react";

export function Agent() {
  const [input, setInput] = useState("");
  const [response, setResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [rfqData, setRfqData] = useState(null);
  const [currentRfqId, setCurrentRfqId] = useState(null);
  const [activeTab, setActiveTab] = useState("request");

  const handleRfqAction = async (actionType, rfqId = null) => {
    if (actionType === 'start' && !input.trim()) {
      toast.error("Please enter an RFQ request", {
        closeButton: true,
        duration: 3000
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("http://localhost:3101/api/langbase", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          input: input || "Status check",
          rfq_id: rfqId,
          action_type: actionType
        })
      });

      // Parse the response (with fallback for non-JSON)
      let data;
      try {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          data = await response.json();
        } else {
          data = await response.text();
        }
      } catch (parseError) {
        console.error("Error parsing response:", parseError);
        data = await response.text();
      }

      // Check for ANY error condition
      if (!response.ok) {
        // Extract the most specific error message
        let errorMessage = `HTTP Error: ${response.status}`;

        if (typeof data === 'string') {
          errorMessage = data || errorMessage;
        } else if (data && typeof data === 'object') {
          errorMessage = data.error || data.message || errorMessage;
        }

        throw new Error(errorMessage);
      }

      // Check for application-level errors
      if (data && typeof data === 'object' && (data.success === false || data.error)) {
        const errorMessage = data.error || data.message || "Application error occurred";
        throw new Error(errorMessage);
      }

      // ONLY for successful responses, update the data
      if (typeof data === 'string') {
        setResponse(data);
        setRfqData(null);
      } else if (data && typeof data === 'object') {
        // Handle different response formats
        const responseText = data.response || data.output || data.message || "";
        setResponse(responseText);

        // Set RFQ data if it exists
        if (data.rfq_state || data.rfq_id) {
          setRfqData(data);
          setCurrentRfqId(data.rfq_state?.rfq_id || data.rfq_id);
          setActiveTab("overview");
        } else {
          setRfqData(null);
        }
      } else {
        setResponse(String(data || ""));
        setRfqData(null);
      }

      // ONLY for successful responses, show success toast
      const actionMessages = {
        start: "RFQ process initiated successfully",
        pause: "RFQ process paused successfully",
        resume: "RFQ process resumed successfully",
        status: "RFQ status retrieved successfully"
      };

      toast.success(actionMessages[actionType] || "RFQ action completed successfully", {
        closeButton: true,
        duration: 3000
      });

    } catch (error) {
      console.error("Error processing RFQ request:", error);

      // Show error toast with specific message
      const errorMessage = error.message || "An error occurred while processing your RFQ request";
      toast.error(errorMessage, {
        closeButton: true,
        duration: Infinity
      });

      // DON'T clear existing data on error
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    handleRfqAction('start');
  };

  const renderRfqOverview = () => {
    if (!rfqData?.rfq_state) return null;

    const { rfq_state, lifecycle_controls } = rfqData;
    const canPause = lifecycle_controls?.can_pause;
    const humanApprovalRequired = lifecycle_controls?.human_approval_required;

    return (
      <div className="space-y-6">
        {/* RFQ Header */}
        <Card className="bg-card border-border">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  RFQ {rfq_state.rfq_id}
                </CardTitle>
                <CardDescription>
                  Created: {new Date(rfq_state.created_at).toLocaleDateString()}
                </CardDescription>
              </div>
              <div className="flex gap-2">
                {rfq_state.status !== 'paused' && canPause && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRfqAction('pause', rfq_state.rfq_id)}
                    disabled={isLoading}
                  >
                    <Pause className="h-4 w-4 mr-1" />
                    Pause
                  </Button>
                )}
                {rfq_state.status === 'paused' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRfqAction('resume', rfq_state.rfq_id)}
                    disabled={isLoading}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Resume
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRfqAction('status', rfq_state.rfq_id)}
                  disabled={isLoading}
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Refresh
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status and Progress */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <Badge variant={
                  rfq_state.status === 'completed' ? 'default' :
                  rfq_state.status === 'paused' ? 'secondary' :
                  rfq_state.status === 'evaluation_complete' ? 'default' : 'outline'
                } className="mt-1">
                  {rfq_state.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Current Step</p>
                <p className="font-medium">{rfq_state.current_step?.replace('_', ' ')}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Progress</p>
                <div className="flex items-center gap-2 mt-1">
                  <Progress value={rfq_state.progress_percentage || 0} className="flex-1" />
                  <span className="text-sm font-medium">{rfq_state.progress_percentage || 0}%</span>
                </div>
              </div>
            </div>

            {/* Budget Information */}
            {rfq_state.metadata && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Budget</p>
                  <p className="font-medium">${(rfq_state.metadata.budget || 0).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Budget Range</p>
                  <p className="font-medium">
                    ${(rfq_state.metadata.budget_range?.min || 0).toLocaleString()} -
                    ${(rfq_state.metadata.budget_range?.max || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            )}

            {/* Human Approval Alert */}
            {humanApprovalRequired && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Human approval required</strong> - This RFQ requires manual review before proceeding to the next step.
                </AlertDescription>
              </Alert>
            )}

            {/* Next Actions */}
            {lifecycle_controls?.next_actions && lifecycle_controls.next_actions.length > 0 && (
              <div>
                <p className="text-sm text-muted-foreground mb-2">Next Actions</p>
                <div className="flex flex-wrap gap-2">
                  {lifecycle_controls.next_actions.map((action, index) => (
                    <Badge key={index} variant="outline">{action.replace('_', ' ')}</Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Suppliers */}
        {rfq_state.suppliers && rfq_state.suppliers.length > 0 && (
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Suppliers ({rfq_state.suppliers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {rfq_state.suppliers.map((supplier, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium">{supplier.name || 'Unknown Supplier'}</h4>
                      <Badge variant={
                        supplier.status === 'responded' ? 'default' :
                        supplier.status === 'declined' ? 'destructive' :
                        supplier.status === 'no_response' ? 'secondary' : 'outline'
                      }>
                        {supplier.status || 'pending'}
                      </Badge>
                    </div>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      {supplier.contact_info?.email && (
                        <p>{supplier.contact_info.email}</p>
                      )}
                      {supplier.score && (
                        <p>Score: {supplier.score}/10</p>
                      )}
                      <div className="flex items-center gap-1">
                        <Shield className="h-3 w-3" />
                        <span className={
                          supplier.risk_level === 'low' ? 'text-green-600' :
                          supplier.risk_level === 'medium' ? 'text-yellow-600' : 'text-red-600'
                        }>
                          {supplier.risk_level || 'unknown'} risk
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quotes */}
        {rfq_state.quotes && rfq_state.quotes.length > 0 && (
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-primary" />
                Quotes Received ({rfq_state.quotes.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {rfq_state.quotes.map((quote, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">Quote {quote.quote_id || `#${index + 1}`}</h4>
                        <p className="text-sm text-muted-foreground">
                          Supplier: {rfq_state.suppliers.find(s => s.supplier_id === quote.supplier_id)?.name || quote.supplier_id || 'Unknown'}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg">${(quote.total_cost || 0).toLocaleString()}</p>
                        {quote.evaluation_score && (
                          <p className="text-sm text-muted-foreground">Score: {quote.evaluation_score}/100</p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Delivery Timeline</p>
                        <p>{quote.delivery_timeline || 'Not specified'}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Received</p>
                        <p>{quote.received_at ? new Date(quote.received_at).toLocaleDateString() : 'Not specified'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Timeline */}
        {rfq_state.timeline && (
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Submission Deadline</p>
                  <p className="font-medium">
                    {rfq_state.timeline.submission_deadline ?
                      new Date(rfq_state.timeline.submission_deadline).toLocaleDateString() :
                      'Not set'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Evaluation Period</p>
                  <p className="font-medium">{rfq_state.timeline.evaluation_period || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Decision Date</p>
                  <p className="font-medium">
                    {rfq_state.timeline.decision_date ?
                      new Date(rfq_state.timeline.decision_date).toLocaleDateString() :
                      'Not set'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  // Extract suppliers from rfq_state or tool_results, if present
  const extractSuppliers = () => {
    const fromRfq = rfqData?.rfq_state?.suppliers ?? [];
    if (fromRfq && fromRfq.length > 0) return fromRfq as any[];

    // Fallback: try find suppliers in tool_results
    try {
      for (const tr of rfqData?.tool_results ?? []) {
        const maybe: any = (tr as any)?.suppliers || (tr as any)?.vendors || (tr as any)?.data?.suppliers || (tr as any)?.data?.vendors;
        if (Array.isArray(maybe) && maybe.length > 0) return maybe as any[];
      }
    } catch (e) {
      // ignore
    }
    return [] as any[];
  };


  const renderToolResults = () => {
    if (!rfqData?.tool_results) return null;

    return (
      <div className="space-y-4">
        {rfqData.tool_results.map((result, index) => (
          <Card key={index} className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-lg">Tool Execution Result #{index + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  {result.status === 'completed' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : result.status === 'error' ? (
                    <XCircle className="h-4 w-4 text-red-600" />
                  ) : (
                    <Clock className="h-4 w-4 text-yellow-600" />
                  )}
                  <Badge variant="outline">{result.status || 'unknown'}</Badge>
                </div>
                <ScrollArea className="h-auto max-h-[300px]">
                  <pre className="text-sm bg-muted p-3 rounded overflow-x-auto">
                    {JSON.stringify(result, null, 2)}
                  </pre>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 md:p-6 lg:p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl md:text-4xl font-bold">SourcingAgent RFQ Management</h1>
          <p className="text-muted-foreground text-lg">
            Stateless RFQ lifecycle management with pause/resume capabilities
          </p>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="request">New RFQ</TabsTrigger>
            <TabsTrigger value="overview" disabled={!rfqData}>Overview</TabsTrigger>
            <TabsTrigger value="analysis" disabled={!response}>Analysis</TabsTrigger>
            <TabsTrigger value="tools" disabled={!((rfqData?.rfq_state?.suppliers?.length ?? 0) > 0 || (rfqData?.tool_results?.length ?? 0) > 0)}>Tools</TabsTrigger>
          </TabsList>

          {/* New RFQ Request Tab */}
          <TabsContent value="request" className="space-y-6">
            <Card className="bg-card border-border">
              <CardHeader>
                <CardTitle>Start New RFQ Process</CardTitle>
                <CardDescription>
                  Describe your procurement requirements to initiate a stateless RFQ workflow
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <Textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Example: We need to source 100 enterprise laptops for our engineering team. Budget is $150,000. Requirements include 16GB RAM, 512GB SSD, and must meet SOC2 compliance. Timeline is 3 weeks for delivery..."
                    className="min-h-[120px] resize-none"
                    disabled={isLoading}
                  />
                  <div className="flex gap-2">
                    <Button
                      type="submit"
                      disabled={isLoading || !input.trim()}
                      className="flex-1 md:flex-none"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
                          Starting RFQ...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Start RFQ Process
                        </>
                      )}
                    </Button>
                    {currentRfqId && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => handleRfqAction('status', currentRfqId)}
                        disabled={isLoading}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Check Status
                      </Button>
                    )}
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* RFQ Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {isLoading ? (
              <Card className="bg-card border-border">
                <CardHeader>
                  <Skeleton className="h-6 w-48" />
                </CardHeader>
                <CardContent className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardContent>
              </Card>
            ) : (
              renderRfqOverview()
            )}
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" className="space-y-6">
            {response && !isLoading && (
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>RFQ Analysis & Recommendations</CardTitle>
                  <CardDescription>
                    Comprehensive analysis based on current RFQ state and market conditions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-auto max-h-[600px]">
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <ReactMarkdown>{response}</ReactMarkdown>
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Tool Results Tab */}
          <TabsContent value="tools" className="space-y-6">
            {/* Vendor listing dashboard */}
            {(extractSuppliers().length > 0) && (
              <VendorDashboard suppliers={extractSuppliers()} />
            )}

            {/* Raw tool results for debugging/export */}
            {renderToolResults()}
          </TabsContent>
        </Tabs>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold">Stateless Processing</h3>
              <p className="text-sm text-muted-foreground">12-Factor compliant</p>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <Play className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold">Pause/Resume</h3>
              <p className="text-sm text-muted-foreground">Lifecycle control</p>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <Building className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold">Supplier Discovery</h3>
              <p className="text-sm text-muted-foreground">Automated sourcing</p>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <TrendingUp className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold">Quote Evaluation</h3>
              <p className="text-sm text-muted-foreground">Intelligent analysis</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}