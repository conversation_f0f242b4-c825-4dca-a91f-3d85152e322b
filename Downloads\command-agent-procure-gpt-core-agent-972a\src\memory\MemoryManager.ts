/**
 * Memory Management System for LangGraph Agent
 * Implements 12-Factor Agent Principle: Own Your Context Window
 * PostgreSQL + pgvector + Redis Integration
 */

import { getDatabaseManager } from '../../database/config';
import { PerplexityEmbedder } from '../integrations/PerplexityIntegration';

// ============================================================================
// MEMORY INTERFACES
// ============================================================================

export interface MemoryContext {
  session_id: string;
  user_id: string;
  context_window: ContextWindow;
  short_term_memory: ShortTermMemory;
  long_term_references: LongTermReference[];
  compression_state: CompressionState;
  created_at: string;
  updated_at: string;
}

export interface ContextWindow {
  current_tokens: number;
  max_tokens: number;
  priority_elements: PriorityElement[];
  compressed_history: CompressedMemory[];
  overflow_buffer: OverflowBuffer;
}

export interface ShortTermMemory {
  active_rfqs: ActiveRFQ[];
  current_session_data: SessionData;
  user_preferences: UserPreferences;
  temporary_calculations: TemporaryData[];
}

export interface LongTermReference {
  memory_id: string;
  memory_type: 'procurement_history' | 'vendor_patterns' | 'compliance_records' | 'user_behavior';
  relevance_score: number;
  last_accessed: string;
  summary: string;
}

export interface PriorityElement {
  id: string;
  content: string;
  priority_score: number;
  token_count: number;
  type: 'system' | 'user' | 'context' | 'memory';
}

export interface CompressedMemory {
  id: string;
  original_content: string;
  compressed_content: string;
  compression_ratio: number;
  importance_score: number;
}

export interface OverflowBuffer {
  items: CompressedMemory[];
  compressed_size: number;
}

export interface CompressionState {
  compression_ratio: number;
  last_compression: string;
  items_compressed: number;
}

export interface ActiveRFQ {
  rfq_id: string;
  status: string;
  current_step: string;
  progress: number;
}

export interface SessionData {
  requests: string[];
  responses: string[];
}

export interface UserPreferences {
  preferred_suppliers: string[];
  budget_ranges: Record<string, number>;
  compliance_requirements: string[];
}

export interface TemporaryData {
  id: string;
  data: any;
  expires_at: string;
}

// ============================================================================
// MEMORY MANAGER CLASS
// ============================================================================

export class MemoryManager {
  private dbManager = getDatabaseManager();
  private embedder = new PerplexityEmbedder();
  private readonly MAX_CONTEXT_TOKENS = 128000; // 128k token limit
  private readonly COMPRESSION_THRESHOLD = 0.8; // Compress when 80% full

  // ========================================================================
  // CONTEXT WINDOW MANAGEMENT
  // ========================================================================

  async initializeMemoryContext(session_id: string, user_id: string): Promise<MemoryContext> {
    // Check for existing context in cache
    const cacheKey = `memory_context:${session_id}`;
    const cached = await this.dbManager.getCache<MemoryContext>(cacheKey);
    
    if (cached) {
      return {
        ...cached,
        updated_at: new Date().toISOString()
      };
    }

    // Load from database or create new
    const existingContext = await this.loadSessionContext(session_id);
    
    if (existingContext) {
      await this.dbManager.setCache(cacheKey, existingContext, 3600);
      return existingContext;
    }

    // Create new memory context
    const newContext: MemoryContext = {
      session_id,
      user_id,
      context_window: {
        current_tokens: 0,
        max_tokens: this.MAX_CONTEXT_TOKENS,
        priority_elements: [],
        compressed_history: [],
        overflow_buffer: { items: [], compressed_size: 0 }
      },
      short_term_memory: {
        active_rfqs: await this.loadActiveRFQs(user_id),
        current_session_data: { requests: [], responses: [] },
        user_preferences: await this.loadUserPreferences(user_id),
        temporary_calculations: []
      },
      long_term_references: [],
      compression_state: {
        compression_ratio: 1.0,
        last_compression: new Date().toISOString(),
        items_compressed: 0
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Cache the new context
    await this.dbManager.setCache(cacheKey, newContext, 3600);
    
    return newContext;
  }

  async optimizeContextWindow(
    memoryContext: MemoryContext,
    relevantMemories: any[],
    patternAnalysis: any
  ): Promise<{
    compressed_context: string;
    relevant_memories: any[];
    tokens_used: number;
    compression_ratio: number;
  }> {
    const { context_window } = memoryContext;
    
    // Calculate current token usage
    let currentTokens = this.estimateTokenCount(JSON.stringify(memoryContext));
    
    // Check if compression is needed
    if (currentTokens > this.MAX_CONTEXT_TOKENS * this.COMPRESSION_THRESHOLD) {
      await this.compressContextWindow(memoryContext);
    }

    // Prioritize and select most relevant information
    const prioritizedElements = await this.prioritizeContextElements(
      memoryContext,
      relevantMemories,
      patternAnalysis
    );

    // Build optimized context
    const optimizedContext = this.buildOptimizedContext(prioritizedElements);
    
    return {
      compressed_context: optimizedContext.content,
      relevant_memories: optimizedContext.memories,
      tokens_used: optimizedContext.tokens,
      compression_ratio: memoryContext.compression_state.compression_ratio
    };
  }

  private async compressContextWindow(memoryContext: MemoryContext): Promise<void> {
    const { context_window } = memoryContext;
    
    // Identify items for compression based on age and importance
    const itemsToCompress = context_window.priority_elements
      .filter(item => item.priority_score < 0.5)
      .sort((a, b) => a.priority_score - b.priority_score);

    let compressedItems = 0;
    let totalCompressionRatio = 0;

    for (const item of itemsToCompress) {
      try {
        // Use AI to compress the content while preserving key information
        const compressed = await this.compressContent(item.content);
        
        const compressedMemory: CompressedMemory = {
          id: item.id,
          original_content: item.content,
          compressed_content: compressed.content,
          compression_ratio: compressed.ratio,
          importance_score: item.priority_score
        };

        context_window.compressed_history.push(compressedMemory);
        totalCompressionRatio += compressed.ratio;
        compressedItems++;

        // Remove from priority elements
        const index = context_window.priority_elements.findIndex(el => el.id === item.id);
        if (index > -1) {
          context_window.priority_elements.splice(index, 1);
        }
      } catch (error) {
        console.error('Error compressing memory item:', error);
      }
    }

    // Update compression state
    if (compressedItems > 0) {
      memoryContext.compression_state = {
        compression_ratio: totalCompressionRatio / compressedItems,
        last_compression: new Date().toISOString(),
        items_compressed: memoryContext.compression_state.items_compressed + compressedItems
      };
    }
  }

  private async compressContent(content: string): Promise<{ content: string; ratio: number }> {
    try {
      // Use Perplexity to compress content while preserving key information
      const compressed = await this.embedder.compressText(content, 0.3); // 30% of original size
      
      return {
        content: compressed,
        ratio: compressed.length / content.length
      };
    } catch (error) {
      console.error('Content compression failed:', error);
      // Fallback: simple truncation
      const truncated = content.substring(0, Math.floor(content.length * 0.5));
      return {
        content: truncated + '...',
        ratio: 0.5
      };
    }
  }

  private async prioritizeContextElements(
    memoryContext: MemoryContext,
    relevantMemories: any[],
    patternAnalysis: any
  ): Promise<PriorityElement[]> {
    const elements: PriorityElement[] = [];

    // Add system context (highest priority)
    elements.push({
      id: 'system_context',
      content: 'System context and current session information',
      priority_score: 1.0,
      token_count: this.estimateTokenCount('System context'),
      type: 'system'
    });

    // Add relevant memories (high priority)
    relevantMemories.forEach((memory, index) => {
      elements.push({
        id: `memory_${index}`,
        content: memory.content || memory.summary,
        priority_score: memory.relevance_score || 0.8,
        token_count: this.estimateTokenCount(memory.content || memory.summary),
        type: 'memory'
      });
    });

    // Add pattern analysis (medium-high priority)
    if (patternAnalysis && patternAnalysis.insights) {
      elements.push({
        id: 'pattern_analysis',
        content: JSON.stringify(patternAnalysis.insights),
        priority_score: 0.7,
        token_count: this.estimateTokenCount(JSON.stringify(patternAnalysis.insights)),
        type: 'context'
      });
    }

    // Add compressed history (lower priority)
    memoryContext.context_window.compressed_history.forEach((compressed, index) => {
      elements.push({
        id: `compressed_${index}`,
        content: compressed.compressed_content,
        priority_score: compressed.importance_score,
        token_count: this.estimateTokenCount(compressed.compressed_content),
        type: 'memory'
      });
    });

    // Sort by priority score (descending)
    return elements.sort((a, b) => b.priority_score - a.priority_score);
  }

  private buildOptimizedContext(elements: PriorityElement[]): {
    content: string;
    memories: any[];
    tokens: number;
  } {
    let totalTokens = 0;
    const selectedElements: PriorityElement[] = [];
    const memories: any[] = [];

    // Select elements within token limit
    for (const element of elements) {
      if (totalTokens + element.token_count <= this.MAX_CONTEXT_TOKENS * 0.9) { // Leave 10% buffer
        selectedElements.push(element);
        totalTokens += element.token_count;
        
        if (element.type === 'memory') {
          memories.push({ content: element.content, priority: element.priority_score });
        }
      }
    }

    // Build context string
    const contextSections = {
      system: selectedElements.filter(e => e.type === 'system').map(e => e.content),
      memories: selectedElements.filter(e => e.type === 'memory').map(e => e.content),
      context: selectedElements.filter(e => e.type === 'context').map(e => e.content)
    };

    const contextContent = [
      '=== SYSTEM CONTEXT ===',
      ...contextSections.system,
      '',
      '=== RELEVANT MEMORIES ===',
      ...contextSections.memories,
      '',
      '=== CONTEXTUAL INFORMATION ===',
      ...contextSections.context
    ].join('\n');

    return {
      content: contextContent,
      memories,
      tokens: totalTokens
    };
  }

  // ========================================================================
  // MEMORY STORAGE AND RETRIEVAL
  // ========================================================================

  async storeWorkingMemory(
    session_id: string,
    agent_id: string,
    user_id: string,
    content: string,
    metadata: any = {}
  ): Promise<string> {
    try {
      // Generate embedding
      const embedding = await this.embedder.embed(content);
      
      // Store in database
      const result = await this.dbManager.query(`
        INSERT INTO working_memory 
        (session_id, agent_id, user_id, message_text, embedding, metadata)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `, [session_id, agent_id, user_id, content, JSON.stringify(embedding), JSON.stringify(metadata)]);

      const memoryId = result.rows[0].id;

      // Cache for quick access
      const cacheKey = `working_memory:${session_id}:${memoryId}`;
      await this.dbManager.setCache(cacheKey, {
        id: memoryId,
        content,
        metadata,
        created_at: new Date().toISOString()
      }, 3600);

      return memoryId;
    } catch (error) {
      console.error('Error storing working memory:', error);
      throw error;
    }
  }

  async storeLongTermMemory(
    user_id: string,
    memory_type: string,
    content: string,
    importance_score: number = 0.5,
    metadata: any = {}
  ): Promise<string> {
    try {
      // Generate embedding
      const embedding = await this.embedder.embed(content);
      
      // Store in database
      const result = await this.dbManager.query(`
        INSERT INTO long_term_memory 
        (user_id, memory_type, content, embedding, importance_score, metadata)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `, [user_id, memory_type, content, JSON.stringify(embedding), importance_score, JSON.stringify(metadata)]);

      return result.rows[0].id;
    } catch (error) {
      console.error('Error storing long-term memory:', error);
      throw error;
    }
  }

  async retrieveSimilarMemories(
    query: string,
    user_id: string,
    memory_types: string[] = [],
    limit: number = 10,
    similarity_threshold: number = 0.8
  ): Promise<any[]> {
    try {
      // Generate query embedding
      const queryEmbedding = await this.embedder.embed(query);
      
      // Search for similar memories
      const result = await this.dbManager.query(`
        SELECT * FROM find_similar_memories($1, $2, $3, $4)
        WHERE (ARRAY_LENGTH($4::text[], 1) IS NULL OR memory_type = ANY($4))
        AND ($5 IS NULL OR user_id = $5)
      `, [JSON.stringify(queryEmbedding), similarity_threshold, limit, memory_types.length > 0 ? memory_types : null, user_id]);

      return result.rows;
    } catch (error) {
      console.error('Error retrieving similar memories:', error);
      return [];
    }
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  private estimateTokenCount(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  private async loadSessionContext(session_id: string): Promise<MemoryContext | null> {
    try {
      const result = await this.dbManager.query(`
        SELECT * FROM working_memory 
        WHERE session_id = $1 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [session_id]);

      if (result.rows.length === 0) {
        return null;
      }

      // Reconstruct memory context from stored data
      // This is a simplified version - in practice, you'd store the full context
      return null; // Return null to create new context
    } catch (error) {
      console.error('Error loading session context:', error);
      return null;
    }
  }

  private async loadActiveRFQs(user_id: string): Promise<ActiveRFQ[]> {
    try {
      const result = await this.dbManager.query(`
        SELECT rfq_id, status, current_step, progress_percentage as progress
        FROM rfq_states 
        WHERE user_id = $1 AND status IN ('active', 'paused')
        ORDER BY updated_at DESC
      `, [user_id]);

      return result.rows;
    } catch (error) {
      console.error('Error loading active RFQs:', error);
      return [];
    }
  }

  private async loadUserPreferences(user_id: string): Promise<UserPreferences> {
    try {
      // Load from cache first
      const cacheKey = `user_preferences:${user_id}`;
      const cached = await this.dbManager.getCache<UserPreferences>(cacheKey);
      
      if (cached) {
        return cached;
      }

      // Load from database or return defaults
      const preferences: UserPreferences = {
        preferred_suppliers: [],
        budget_ranges: {},
        compliance_requirements: []
      };

      // Cache for future use
      await this.dbManager.setCache(cacheKey, preferences, 7200); // 2 hours

      return preferences;
    } catch (error) {
      console.error('Error loading user preferences:', error);
      return {
        preferred_suppliers: [],
        budget_ranges: {},
        compliance_requirements: []
      };
    }
  }

  // ========================================================================
  // CLEANUP AND MAINTENANCE
  // ========================================================================

  async cleanupExpiredMemories(): Promise<number> {
    try {
      const result = await this.dbManager.query('SELECT cleanup_expired_working_memory()');
      const deletedCount = result.rows[0].cleanup_expired_working_memory;
      
      console.log(`Cleaned up ${deletedCount} expired working memories`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up expired memories:', error);
      return 0;
    }
  }

  async updateMemoryImportance(memory_id: string, importance_score: number): Promise<void> {
    try {
      await this.dbManager.query(`
        UPDATE long_term_memory 
        SET importance_score = $1, updated_at = NOW()
        WHERE id = $2
      `, [importance_score, memory_id]);
    } catch (error) {
      console.error('Error updating memory importance:', error);
    }
  }
}
