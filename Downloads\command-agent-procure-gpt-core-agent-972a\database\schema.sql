-- PostgreSQL 18 + pgvector Schema for LangGraph Agent System
-- Enhanced AI Agent Refactoring: From Langbase to LangGraph with 12-Factor Agent Principles

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- ============================================================================
-- MEMORY MANAGEMENT TABLES (12-Factor Principle: Own Your Context Window)
-- ============================================================================

-- Working Memory (Short-term Memory)
CREATE TABLE working_memory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    message_text TEXT NOT NULL,
    embedding vector(1536), -- Perplexity embedding dimensions
    metadata JSONB DEFAULT '{}',
    importance_score FLOAT DEFAULT 0.0,
    access_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '24 hours')
);

-- Indexes for working memory
CREATE INDEX idx_working_memory_session ON working_memory(session_id);
CREATE INDEX idx_working_memory_agent ON working_memory(agent_id);
CREATE INDEX idx_working_memory_user ON working_memory(user_id);
CREATE INDEX idx_working_memory_embedding ON working_memory USING HNSW (embedding vector_cosine_ops);
CREATE INDEX idx_working_memory_expires ON working_memory(expires_at);
CREATE INDEX idx_working_memory_importance ON working_memory(importance_score DESC);

-- Long-term Memory (Persistent Memory)
CREATE TABLE long_term_memory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255),
    memory_type VARCHAR(50) NOT NULL, -- episodic, semantic, procedural
    content TEXT NOT NULL,
    embedding vector(1536),
    importance_score FLOAT DEFAULT 0.0,
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for long-term memory
CREATE INDEX idx_long_term_memory_user ON long_term_memory(user_id);
CREATE INDEX idx_long_term_memory_type ON long_term_memory(memory_type);
CREATE INDEX idx_long_term_memory_embedding ON long_term_memory USING HNSW (embedding vector_cosine_ops);
CREATE INDEX idx_long_term_memory_importance ON long_term_memory(importance_score DESC);
CREATE INDEX idx_long_term_memory_tags ON long_term_memory USING GIN(tags);
CREATE INDEX idx_long_term_memory_access ON long_term_memory(last_accessed DESC);

-- Episodic Memory (Specific interactions and events)
CREATE TABLE episodic_memory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    episode_summary TEXT NOT NULL,
    embedding vector(1536),
    participants TEXT[] DEFAULT '{}',
    outcome VARCHAR(100),
    context_metadata JSONB DEFAULT '{}',
    importance_score FLOAT DEFAULT 0.0,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for episodic memory
CREATE INDEX idx_episodic_memory_user ON episodic_memory(user_id);
CREATE INDEX idx_episodic_memory_embedding ON episodic_memory USING HNSW (embedding vector_cosine_ops);
CREATE INDEX idx_episodic_memory_timestamp ON episodic_memory(timestamp DESC);
CREATE INDEX idx_episodic_memory_participants ON episodic_memory USING GIN(participants);

-- Procedural Memory (Learned procedures and workflows)
CREATE TABLE procedural_memory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    procedure_name VARCHAR(255) NOT NULL,
    steps JSONB NOT NULL,
    success_rate FLOAT DEFAULT 0.0,
    embedding vector(1536),
    usage_count INTEGER DEFAULT 0,
    last_optimized TIMESTAMP DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for procedural memory
CREATE INDEX idx_procedural_memory_name ON procedural_memory(procedure_name);
CREATE INDEX idx_procedural_memory_embedding ON procedural_memory USING HNSW (embedding vector_cosine_ops);
CREATE INDEX idx_procedural_memory_success_rate ON procedural_memory(success_rate DESC);
CREATE INDEX idx_procedural_memory_usage ON procedural_memory(usage_count DESC);

-- ============================================================================
-- AGENT STATE MANAGEMENT (12-Factor Principle: Unify Execution State and Business State)
-- ============================================================================

-- Agent State Storage
CREATE TABLE agent_states (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    agent_type VARCHAR(100) NOT NULL,
    current_step VARCHAR(255),
    state_data JSONB NOT NULL DEFAULT '{}',
    checkpoint_data JSONB DEFAULT '{}',
    version INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active', -- active, paused, completed, error
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for agent states
CREATE INDEX idx_agent_states_session ON agent_states(session_id);
CREATE INDEX idx_agent_states_user ON agent_states(user_id);
CREATE INDEX idx_agent_states_type ON agent_states(agent_type);
CREATE INDEX idx_agent_states_status ON agent_states(status);
CREATE INDEX idx_agent_states_updated ON agent_states(updated_at DESC);

-- Checkpoints for pause/resume functionality
CREATE TABLE agent_checkpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_state_id UUID NOT NULL REFERENCES agent_states(id) ON DELETE CASCADE,
    checkpoint_name VARCHAR(255) NOT NULL,
    step_name VARCHAR(255) NOT NULL,
    state_snapshot JSONB NOT NULL,
    can_resume_from BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for checkpoints
CREATE INDEX idx_agent_checkpoints_state ON agent_checkpoints(agent_state_id);
CREATE INDEX idx_agent_checkpoints_name ON agent_checkpoints(checkpoint_name);
CREATE INDEX idx_agent_checkpoints_resume ON agent_checkpoints(can_resume_from);
CREATE INDEX idx_agent_checkpoints_created ON agent_checkpoints(created_at DESC);

-- ============================================================================
-- RFQ MANAGEMENT TABLES (Business Logic State)
-- ============================================================================

-- RFQ State Management
CREATE TABLE rfq_states (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rfq_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'draft', -- draft, active, paused, evaluation, completed, cancelled
    current_step VARCHAR(255),
    progress_percentage INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    budget DECIMAL(15,2),
    budget_range JSONB, -- {min: number, max: number}
    timeline JSONB, -- submission_deadline, evaluation_period, decision_date
    requirements TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for RFQ states
CREATE INDEX idx_rfq_states_rfq_id ON rfq_states(rfq_id);
CREATE INDEX idx_rfq_states_user ON rfq_states(user_id);
CREATE INDEX idx_rfq_states_session ON rfq_states(session_id);
CREATE INDEX idx_rfq_states_status ON rfq_states(status);
CREATE INDEX idx_rfq_states_updated ON rfq_states(updated_at DESC);

-- Suppliers/Vendors
CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    supplier_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    contact_info JSONB DEFAULT '{}', -- email, phone, address
    capabilities TEXT[] DEFAULT '{}',
    risk_level VARCHAR(20) DEFAULT 'unknown', -- low, medium, high, unknown
    performance_score FLOAT DEFAULT 0.0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for suppliers
CREATE INDEX idx_suppliers_supplier_id ON suppliers(supplier_id);
CREATE INDEX idx_suppliers_name ON suppliers(name);
CREATE INDEX idx_suppliers_risk_level ON suppliers(risk_level);
CREATE INDEX idx_suppliers_performance ON suppliers(performance_score DESC);
CREATE INDEX idx_suppliers_capabilities ON suppliers USING GIN(capabilities);

-- RFQ-Supplier Relationships
CREATE TABLE rfq_suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rfq_id VARCHAR(255) NOT NULL REFERENCES rfq_states(rfq_id) ON DELETE CASCADE,
    supplier_id VARCHAR(255) NOT NULL REFERENCES suppliers(supplier_id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'invited', -- invited, responded, declined, no_response
    score FLOAT DEFAULT 0.0,
    invited_at TIMESTAMP DEFAULT NOW(),
    responded_at TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    UNIQUE(rfq_id, supplier_id)
);

-- Indexes for RFQ-supplier relationships
CREATE INDEX idx_rfq_suppliers_rfq ON rfq_suppliers(rfq_id);
CREATE INDEX idx_rfq_suppliers_supplier ON rfq_suppliers(supplier_id);
CREATE INDEX idx_rfq_suppliers_status ON rfq_suppliers(status);

-- Quotes
CREATE TABLE quotes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quote_id VARCHAR(255) UNIQUE NOT NULL,
    rfq_id VARCHAR(255) NOT NULL REFERENCES rfq_states(rfq_id) ON DELETE CASCADE,
    supplier_id VARCHAR(255) NOT NULL REFERENCES suppliers(supplier_id) ON DELETE CASCADE,
    total_cost DECIMAL(15,2) NOT NULL,
    delivery_timeline VARCHAR(255),
    evaluation_score FLOAT DEFAULT 0.0,
    quote_data JSONB DEFAULT '{}', -- detailed quote information
    received_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for quotes
CREATE INDEX idx_quotes_quote_id ON quotes(quote_id);
CREATE INDEX idx_quotes_rfq ON quotes(rfq_id);
CREATE INDEX idx_quotes_supplier ON quotes(supplier_id);
CREATE INDEX idx_quotes_cost ON quotes(total_cost);
CREATE INDEX idx_quotes_score ON quotes(evaluation_score DESC);

-- ============================================================================
-- PATTERN RECOGNITION AND ANALYTICS
-- ============================================================================

-- Pattern Analysis Storage
CREATE TABLE pattern_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pattern_id VARCHAR(255) UNIQUE NOT NULL,
    pattern_type VARCHAR(100) NOT NULL, -- vendor_performance, budget_trends, compliance_issues, seasonal_patterns
    confidence_score FLOAT NOT NULL,
    occurrences INTEGER DEFAULT 1,
    time_window VARCHAR(100),
    insights JSONB DEFAULT '{}',
    regional_context JSONB DEFAULT '{}',
    supporting_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for pattern analysis
CREATE INDEX idx_pattern_analysis_pattern_id ON pattern_analysis(pattern_id);
CREATE INDEX idx_pattern_analysis_type ON pattern_analysis(pattern_type);
CREATE INDEX idx_pattern_analysis_confidence ON pattern_analysis(confidence_score DESC);
CREATE INDEX idx_pattern_analysis_occurrences ON pattern_analysis(occurrences DESC);

-- ============================================================================
-- TOOL EXECUTION AND RESULTS
-- ============================================================================

-- Tool Execution Log
CREATE TABLE tool_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    tool_name VARCHAR(255) NOT NULL,
    input_data JSONB NOT NULL,
    output_data JSONB,
    status VARCHAR(50) DEFAULT 'pending', -- pending, completed, error, timeout
    execution_time_ms INTEGER,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Indexes for tool executions
CREATE INDEX idx_tool_executions_session ON tool_executions(session_id);
CREATE INDEX idx_tool_executions_agent ON tool_executions(agent_id);
CREATE INDEX idx_tool_executions_tool ON tool_executions(tool_name);
CREATE INDEX idx_tool_executions_status ON tool_executions(status);
CREATE INDEX idx_tool_executions_created ON tool_executions(created_at DESC);

-- ============================================================================
-- PROMPT MANAGEMENT (12-Factor Principle: Own Your Prompts)
-- ============================================================================

-- Prompt Templates Storage
CREATE TABLE prompt_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_name VARCHAR(255) UNIQUE NOT NULL,
    version VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    variables JSONB DEFAULT '{}', -- variable definitions and defaults
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for prompt templates
CREATE INDEX idx_prompt_templates_name ON prompt_templates(template_name);
CREATE INDEX idx_prompt_templates_version ON prompt_templates(version);
CREATE INDEX idx_prompt_templates_active ON prompt_templates(is_active);

-- Prompt Performance Metrics
CREATE TABLE prompt_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_name VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    usage_count INTEGER DEFAULT 0,
    success_rate FLOAT DEFAULT 0.0,
    avg_response_time_ms INTEGER DEFAULT 0,
    user_satisfaction_score FLOAT DEFAULT 0.0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (template_name) REFERENCES prompt_templates(template_name)
);

-- Indexes for prompt metrics
CREATE INDEX idx_prompt_metrics_template ON prompt_metrics(template_name);
CREATE INDEX idx_prompt_metrics_version ON prompt_metrics(version);
CREATE INDEX idx_prompt_metrics_success_rate ON prompt_metrics(success_rate DESC);

-- ============================================================================
-- AUDIT AND LOGGING
-- ============================================================================

-- System Audit Log
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for audit log
CREATE INDEX idx_audit_log_user ON audit_log(user_id);
CREATE INDEX idx_audit_log_session ON audit_log(session_id);
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_resource ON audit_log(resource_type, resource_id);
CREATE INDEX idx_audit_log_created ON audit_log(created_at DESC);

-- ============================================================================
-- FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_working_memory_updated_at BEFORE UPDATE ON working_memory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_long_term_memory_updated_at BEFORE UPDATE ON long_term_memory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_procedural_memory_updated_at BEFORE UPDATE ON procedural_memory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_states_updated_at BEFORE UPDATE ON agent_states FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rfq_states_updated_at BEFORE UPDATE ON rfq_states FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pattern_analysis_updated_at BEFORE UPDATE ON pattern_analysis FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prompt_templates_updated_at BEFORE UPDATE ON prompt_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prompt_metrics_updated_at BEFORE UPDATE ON prompt_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function for semantic similarity search
CREATE OR REPLACE FUNCTION find_similar_memories(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.8,
    max_results int DEFAULT 10,
    memory_types text[] DEFAULT NULL
)
RETURNS TABLE (
    id uuid,
    content text,
    similarity_score float,
    memory_type varchar(50),
    metadata jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ltm.id,
        ltm.content,
        1 - (ltm.embedding <=> query_embedding) as similarity_score,
        ltm.memory_type,
        ltm.metadata
    FROM long_term_memory ltm
    WHERE 
        (memory_types IS NULL OR ltm.memory_type = ANY(memory_types))
        AND (1 - (ltm.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY ltm.embedding <=> query_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired working memory
CREATE OR REPLACE FUNCTION cleanup_expired_working_memory()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM working_memory WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- INITIAL DATA AND CONFIGURATION
-- ============================================================================

-- Insert default prompt templates
INSERT INTO prompt_templates (template_name, version, content, variables, metadata) VALUES
('procurement_analysis', '1.0', 
'You are an Advanced Procurement Agent with comprehensive memory and pattern recognition capabilities.

MEMORY CONTEXT:
{{memory_context}}

HISTORICAL PATTERNS DETECTED:
{{historical_patterns}}

RELEVANT PAST EXPERIENCES:
{{relevant_memories}}

REGIONAL INSIGHTS:
{{regional_insights}}

Based on this comprehensive context, analyze the procurement request and provide:

1. **Memory-Enhanced Analysis**: How past experiences inform this decision
2. **Pattern-Based Insights**: Relevant trends and predictions
3. **Risk Assessment**: Based on historical data and patterns
4. **Vendor Recommendations**: Leveraging performance history
5. **Cost Optimization**: Using historical pricing patterns
6. **Compliance Guidance**: Based on past compliance issues
7. **Timeline Predictions**: Using historical delivery patterns
8. **Regional Considerations**: Location-specific factors

Provide actionable insights that demonstrate learning from past procurement activities.',
'{"memory_context": "", "historical_patterns": "", "relevant_memories": "", "regional_insights": ""}',
'{"category": "procurement", "version": "1.0", "author": "system"}'),

('error_recovery', '1.0',
'You are an Error Recovery Agent with memory context. The workflow encountered an error but we have memory context to provide helpful guidance.

Last Checkpoint: {{last_checkpoint}}
Error Context: {{error_message}}

Provide helpful guidance and suggest recovery options based on available memory context.',
'{"last_checkpoint": "", "error_message": ""}',
'{"category": "error_handling", "version": "1.0", "author": "system"}'),

('proactive_insights', '1.0',
'You are a Proactive Insights Agent. Based on the analysis and detected patterns, generate forward-looking insights and recommendations.

Pattern Analysis: {{pattern_analysis}}
Current Analysis: {{current_analysis}}

Generate proactive insights including:
1. **Predictive Alerts**: Potential issues based on patterns
2. **Optimization Opportunities**: Cost and process improvements
3. **Market Intelligence**: Trends affecting future procurement
4. **Vendor Performance Predictions**: Based on historical data
5. **Compliance Forecasting**: Potential regulatory changes
6. **Regional Market Dynamics**: Location-specific trends

Focus on actionable insights that prevent problems and optimize outcomes.',
'{"pattern_analysis": "", "current_analysis": ""}',
'{"category": "insights", "version": "1.0", "author": "system"}');

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_working_memory_composite ON working_memory(session_id, agent_id, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_agent_states_composite ON agent_states(user_id, status, updated_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rfq_states_composite ON rfq_states(user_id, status, updated_at DESC);

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO your_app_user;

COMMENT ON SCHEMA public IS 'LangGraph Agent System with 12-Factor Principles and PostgreSQL + pgvector Memory Management';
