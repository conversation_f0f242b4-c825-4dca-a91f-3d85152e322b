/**
 * LangGraph Agent System - Core Architecture
 * Implements 12-Factor Agent Principles with Multi-Agent StateGraph
 * Replaces Langbase workflow with LangGraph's stateful architecture
 */

import { StateGraph, END, START } from '@langchain/langgraph';
import { BaseMessage, HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { Annotation } from '@langchain/langgraph';
import { MemoryManager } from '../memory/MemoryManager';
import { PerplexityIntegration } from '../integrations/PerplexityIntegration';
import { getDatabaseManager } from '../../database/config';
// Note: PostgreSQLCheckpointer will be implemented separately

// ============================================================================
// AGENT STATE DEFINITION
// ============================================================================

// Define the state schema for our agent system
const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  current_agent: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => 'coordinator'
  }),
  memory_context: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  user_id: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ''
  }),
  session_id: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => ''
  }),
  rfq_data: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  execution_metadata: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  tool_results: Annotation<any[]>({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  pattern_analysis: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({})
  }),
  relevant_memories: Annotation<any[]>({
    reducer: (x, y) => y ?? x,
    default: () => []
  }),
  workflow_status: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => 'active'
  }),
  human_approval_required: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false
  }),
  error_context: Annotation<any>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => null
  })
});

type AgentStateType = typeof AgentState.State;

// ============================================================================
// LANGGRAPH AGENT SYSTEM CLASS
// ============================================================================

export class LangGraphAgentSystem {
  private memoryManager: MemoryManager;
  private perplexity: PerplexityIntegration;
  private dbManager = getDatabaseManager();
  private checkpointer: PostgreSQLCheckpointer;
  private graph: any;

  constructor() {
    this.memoryManager = new MemoryManager();
    this.perplexity = new PerplexityIntegration();
    this.checkpointer = new PostgreSQLCheckpointer();
    this.graph = this.createAgentGraph();
  }

  // ========================================================================
  // GRAPH CONSTRUCTION
  // ========================================================================

  private createAgentGraph() {
    // Create the state graph
    const workflow = new StateGraph(AgentState);

    // Add agent nodes
    workflow.addNode('coordinator', this.coordinatorAgent.bind(this));
    workflow.addNode('memory_agent', this.memoryAgent.bind(this));
    workflow.addNode('retrieval_agent', this.retrievalAgent.bind(this));
    workflow.addNode('reasoning_agent', this.reasoningAgent.bind(this));
    workflow.addNode('procurement_agent', this.procurementAgent.bind(this));
    workflow.addNode('pattern_agent', this.patternAgent.bind(this));
    workflow.addNode('human_approval', this.humanApprovalAgent.bind(this));
    workflow.addNode('error_recovery', this.errorRecoveryAgent.bind(this));

    // Set entry point
    workflow.addEdge(START, 'coordinator');

    // Add conditional edges from coordinator
    workflow.addConditionalEdges(
      'coordinator',
      this.routeFromCoordinator.bind(this),
      {
        'memory': 'memory_agent',
        'retrieval': 'retrieval_agent',
        'reasoning': 'reasoning_agent',
        'procurement': 'procurement_agent',
        'pattern': 'pattern_agent',
        'human_approval': 'human_approval',
        'error_recovery': 'error_recovery',
        'end': END
      }
    );

    // Add edges back to coordinator from specialized agents
    workflow.addEdge('memory_agent', 'coordinator');
    workflow.addEdge('retrieval_agent', 'coordinator');
    workflow.addEdge('reasoning_agent', 'coordinator');
    workflow.addEdge('procurement_agent', 'coordinator');
    workflow.addEdge('pattern_agent', 'coordinator');
    workflow.addEdge('human_approval', 'coordinator');
    workflow.addEdge('error_recovery', 'coordinator');

    // Compile with checkpointer
    return workflow.compile({
      checkpointer: this.checkpointer
    });
  }

  // ========================================================================
  // ROUTING LOGIC
  // ========================================================================

  private async routeFromCoordinator(state: AgentStateType): Promise<string> {
    try {
      // Check for error conditions first
      if (state.error_context) {
        return 'error_recovery';
      }

      // Check for human approval requirements
      if (state.human_approval_required) {
        return 'human_approval';
      }

      // Determine next agent based on current state and workflow progress
      const lastMessage = state.messages[state.messages.length - 1];
      const messageContent = lastMessage?.content || '';

      // Use Perplexity to determine routing
      const routingDecision = await this.perplexity.generateResponse([
        {
          role: 'system',
          content: `You are a workflow coordinator. Based on the current state and last message, determine which specialized agent should handle the next step.

Available agents:
- memory: Initialize or update memory context
- retrieval: Retrieve relevant information from memory/database
- reasoning: Perform analysis and reasoning
- procurement: Handle RFQ-specific processing
- pattern: Analyze patterns and generate insights
- human_approval: Route to human for approval
- end: Complete the workflow

Current workflow status: ${state.workflow_status}
Current agent: ${state.current_agent}
Has memory context: ${Object.keys(state.memory_context).length > 0}
Has RFQ data: ${Object.keys(state.rfq_data).length > 0}

Respond with only the agent name.`
        },
        {
          role: 'user',
          content: `Last message: ${messageContent}\n\nWhich agent should handle this next?`
        }
      ], {
        temperature: 0.1,
        max_tokens: 50
      });

      const route = routingDecision.trim().toLowerCase();
      
      // Validate route
      const validRoutes = ['memory', 'retrieval', 'reasoning', 'procurement', 'pattern', 'human_approval', 'end'];
      if (validRoutes.includes(route)) {
        return route;
      }

      // Default fallback routing logic
      if (!state.memory_context || Object.keys(state.memory_context).length === 0) {
        return 'memory';
      }

      if (state.relevant_memories.length === 0) {
        return 'retrieval';
      }

      if (!state.rfq_data || Object.keys(state.rfq_data).length === 0) {
        return 'procurement';
      }

      if (!state.pattern_analysis || Object.keys(state.pattern_analysis).length === 0) {
        return 'pattern';
      }

      return 'reasoning';
    } catch (error) {
      console.error('Routing error:', error);
      return 'error_recovery';
    }
  }

  // ========================================================================
  // SPECIALIZED AGENT IMPLEMENTATIONS
  // ========================================================================

  private async coordinatorAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log(`Coordinator: Current agent is ${state.current_agent}, workflow status: ${state.workflow_status}`);
      
      // Update execution metadata
      const metadata = {
        ...state.execution_metadata,
        last_coordinator_run: new Date().toISOString(),
        total_messages: state.messages.length
      };

      // Check if workflow should end
      const shouldEnd = await this.shouldEndWorkflow(state);
      
      if (shouldEnd) {
        return {
          current_agent: 'coordinator',
          workflow_status: 'completed',
          execution_metadata: metadata
        };
      }

      return {
        current_agent: 'coordinator',
        execution_metadata: metadata
      };
    } catch (error) {
      console.error('Coordinator agent error:', error);
      return {
        error_context: { agent: 'coordinator', error: error.message },
        workflow_status: 'error'
      };
    }
  }

  private async memoryAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Memory Agent: Initializing memory context');
      
      // Initialize memory context
      const memoryContext = await this.memoryManager.initializeMemoryContext(
        state.session_id,
        state.user_id
      );

      // Add system message about memory initialization
      const systemMessage = new SystemMessage(
        `Memory context initialized for session ${state.session_id}. ` +
        `Active RFQs: ${memoryContext.short_term_memory.active_rfqs.length}, ` +
        `Context tokens: ${memoryContext.context_window.current_tokens}`
      );

      return {
        messages: [systemMessage],
        memory_context: memoryContext,
        current_agent: 'memory_agent'
      };
    } catch (error) {
      console.error('Memory agent error:', error);
      return {
        error_context: { agent: 'memory', error: error.message }
      };
    }
  }

  private async retrievalAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Retrieval Agent: Retrieving relevant memories');
      
      const lastMessage = state.messages[state.messages.length - 1];
      const query = lastMessage?.content || '';

      // Retrieve similar memories
      const relevantMemories = await this.memoryManager.retrieveSimilarMemories(
        query,
        state.user_id,
        ['procurement_history', 'vendor_patterns', 'compliance_records'],
        10,
        0.7
      );

      // Add retrieval results message
      const retrievalMessage = new AIMessage(
        `Retrieved ${relevantMemories.length} relevant memories from past procurement activities.`
      );

      return {
        messages: [retrievalMessage],
        relevant_memories: relevantMemories,
        current_agent: 'retrieval_agent'
      };
    } catch (error) {
      console.error('Retrieval agent error:', error);
      return {
        error_context: { agent: 'retrieval', error: error.message }
      };
    }
  }

  private async reasoningAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Reasoning Agent: Performing analysis and reasoning');
      
      const lastMessage = state.messages[state.messages.length - 1];
      const userQuery = lastMessage?.content || '';

      // Load prompt template
      const promptTemplate = await this.perplexity.loadPromptTemplate('procurement_analysis');
      
      // Render prompt with context
      const prompt = this.perplexity.renderPromptTemplate(promptTemplate, {
        memory_context: JSON.stringify(state.memory_context),
        historical_patterns: JSON.stringify(state.pattern_analysis),
        relevant_memories: state.relevant_memories.map(m => `- ${m.content}`).join('\n'),
        regional_insights: 'Mangaluru market insights'
      });

      // Generate reasoning response
      const response = await this.perplexity.generateResponse([
        { role: 'system', content: prompt },
        { role: 'user', content: userQuery }
      ], {
        temperature: 0.2,
        max_tokens: 3000
      });

      const reasoningMessage = new AIMessage(response);

      return {
        messages: [reasoningMessage],
        current_agent: 'reasoning_agent'
      };
    } catch (error) {
      console.error('Reasoning agent error:', error);
      return {
        error_context: { agent: 'reasoning', error: error.message }
      };
    }
  }

  private async procurementAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Procurement Agent: Processing RFQ-specific logic');
      
      const lastMessage = state.messages[state.messages.length - 1];
      const userInput = lastMessage?.content || '';

      // Extract RFQ information and create RFQ state
      const rfqData = await this.processRFQRequest(userInput, state);

      // Store RFQ state in database
      await this.storeRFQState(rfqData, state.user_id, state.session_id);

      const procurementMessage = new AIMessage(
        `RFQ ${rfqData.rfq_id} has been processed. Status: ${rfqData.status}, Progress: ${rfqData.progress_percentage}%`
      );

      return {
        messages: [procurementMessage],
        rfq_data: rfqData,
        current_agent: 'procurement_agent'
      };
    } catch (error) {
      console.error('Procurement agent error:', error);
      return {
        error_context: { agent: 'procurement', error: error.message }
      };
    }
  }

  private async patternAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Pattern Agent: Analyzing historical patterns');
      
      // Analyze patterns based on user history and current request
      const patternAnalysis = await this.analyzePatterns(state);

      const patternMessage = new AIMessage(
        `Pattern analysis completed. Detected ${patternAnalysis.patterns?.length || 0} relevant patterns.`
      );

      return {
        messages: [patternMessage],
        pattern_analysis: patternAnalysis,
        current_agent: 'pattern_agent'
      };
    } catch (error) {
      console.error('Pattern agent error:', error);
      return {
        error_context: { agent: 'pattern', error: error.message }
      };
    }
  }

  private async humanApprovalAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Human Approval Agent: Requesting human intervention');
      
      // Create human approval request
      const approvalRequest = {
        session_id: state.session_id,
        user_id: state.user_id,
        rfq_data: state.rfq_data,
        reason: 'High-value procurement requires human approval',
        created_at: new Date().toISOString()
      };

      // Store approval request
      await this.storeApprovalRequest(approvalRequest);

      const approvalMessage = new SystemMessage(
        'Human approval required. The workflow has been paused pending manual review.'
      );

      return {
        messages: [approvalMessage],
        workflow_status: 'paused',
        human_approval_required: false, // Reset flag
        current_agent: 'human_approval'
      };
    } catch (error) {
      console.error('Human approval agent error:', error);
      return {
        error_context: { agent: 'human_approval', error: error.message }
      };
    }
  }

  private async errorRecoveryAgent(state: AgentStateType): Promise<Partial<AgentStateType>> {
    try {
      console.log('Error Recovery Agent: Handling error condition');
      
      const errorContext = state.error_context;
      
      // Load error recovery prompt
      const promptTemplate = await this.perplexity.loadPromptTemplate('error_recovery');
      const prompt = this.perplexity.renderPromptTemplate(promptTemplate, {
        last_checkpoint: JSON.stringify(state.execution_metadata),
        error_message: errorContext?.error || 'Unknown error'
      });

      // Generate recovery guidance
      const recoveryResponse = await this.perplexity.generateResponse([
        { role: 'system', content: prompt },
        { role: 'user', content: `Error occurred in ${errorContext?.agent || 'unknown'} agent. Please provide recovery guidance.` }
      ]);

      const recoveryMessage = new AIMessage(recoveryResponse);

      return {
        messages: [recoveryMessage],
        workflow_status: 'recovered',
        error_context: null, // Clear error
        current_agent: 'error_recovery'
      };
    } catch (error) {
      console.error('Error recovery agent error:', error);
      return {
        workflow_status: 'failed',
        error_context: { agent: 'error_recovery', error: error.message }
      };
    }
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  private async shouldEndWorkflow(state: AgentStateType): Promise<boolean> {
    // End conditions
    if (state.workflow_status === 'completed' || state.workflow_status === 'failed') {
      return true;
    }

    // Check if we have sufficient information to provide a complete response
    const hasMemoryContext = state.memory_context && Object.keys(state.memory_context).length > 0;
    const hasRelevantMemories = state.relevant_memories && state.relevant_memories.length > 0;
    const hasRFQData = state.rfq_data && Object.keys(state.rfq_data).length > 0;
    const hasPatternAnalysis = state.pattern_analysis && Object.keys(state.pattern_analysis).length > 0;
    const hasReasoningResponse = state.messages.some(m => m.constructor.name === 'AIMessage' && m.content.length > 100);

    return hasMemoryContext && hasRelevantMemories && hasRFQData && hasPatternAnalysis && hasReasoningResponse;
  }

  private async processRFQRequest(input: string, state: AgentStateType): Promise<any> {
    // Extract RFQ information from input
    const rfqId = `RFQ-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      rfq_id: rfqId,
      status: 'active',
      current_step: 'analysis',
      progress_percentage: 25,
      metadata: {
        budget: null,
        budget_range: null,
        requirements: input
      },
      suppliers: [],
      quotes: [],
      timeline: {
        submission_deadline: null,
        evaluation_period: null,
        decision_date: null
      }
    };
  }

  private async storeRFQState(rfqData: any, userId: string, sessionId: string): Promise<void> {
    try {
      await this.dbManager.query(`
        INSERT INTO rfq_states (rfq_id, user_id, session_id, status, current_step, progress_percentage, metadata, requirements)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (rfq_id) DO UPDATE SET
          status = EXCLUDED.status,
          current_step = EXCLUDED.current_step,
          progress_percentage = EXCLUDED.progress_percentage,
          metadata = EXCLUDED.metadata,
          updated_at = NOW()
      `, [
        rfqData.rfq_id,
        userId,
        sessionId,
        rfqData.status,
        rfqData.current_step,
        rfqData.progress_percentage,
        JSON.stringify(rfqData.metadata),
        rfqData.metadata.requirements
      ]);
    } catch (error) {
      console.error('Error storing RFQ state:', error);
    }
  }

  private async analyzePatterns(state: AgentStateType): Promise<any> {
    // Simplified pattern analysis - in production, this would be more sophisticated
    return {
      patterns: [
        {
          pattern_id: 'budget_trend_1',
          pattern_type: 'budget_trends',
          confidence_score: 0.85,
          insights: ['Budget requests tend to increase by 15% year-over-year']
        }
      ],
      regional_insights: 'Mangaluru market shows strong supplier availability for technology procurement'
    };
  }

  private async storeApprovalRequest(request: any): Promise<void> {
    try {
      await this.dbManager.query(`
        INSERT INTO tool_executions (session_id, agent_id, tool_name, input_data, status, metadata)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [
        request.session_id,
        'human_approval',
        'approval_request',
        JSON.stringify(request),
        'pending',
        { type: 'human_approval', created_at: request.created_at }
      ]);
    } catch (error) {
      console.error('Error storing approval request:', error);
    }
  }

  // ========================================================================
  // PUBLIC API
  // ========================================================================

  async processRequest(
    input: string,
    userId: string,
    sessionId: string,
    options: {
      rfqId?: string;
      actionType?: string;
    } = {}
  ): Promise<any> {
    try {
      // Create initial state
      const initialState: Partial<AgentStateType> = {
        messages: [new HumanMessage(input)],
        user_id: userId,
        session_id: sessionId,
        workflow_status: 'active',
        execution_metadata: {
          start_time: new Date().toISOString(),
          rfq_id: options.rfqId,
          action_type: options.actionType
        }
      };

      // Execute the graph
      const config = {
        configurable: {
          thread_id: sessionId
        }
      };

      const result = await this.graph.invoke(initialState, config);
      
      return {
        response: this.extractFinalResponse(result),
        rfq_state: result.rfq_data,
        memory_stats: {
          memories_recalled: result.relevant_memories?.length || 0,
          patterns_detected: result.pattern_analysis?.patterns?.length || 0,
          workflow_status: result.workflow_status
        },
        tool_results: result.tool_results || []
      };
    } catch (error) {
      console.error('LangGraph processing error:', error);
      throw error;
    }
  }

  private extractFinalResponse(result: AgentStateType): string {
    // Extract the most relevant AI response from the message history
    const aiMessages = result.messages.filter(m => m.constructor.name === 'AIMessage');
    const lastAIMessage = aiMessages[aiMessages.length - 1];
    
    return lastAIMessage?.content || 'Processing completed successfully.';
  }

  async pauseWorkflow(sessionId: string): Promise<void> {
    // Implementation for pausing workflow
    console.log(`Pausing workflow for session: ${sessionId}`);
  }

  async resumeWorkflow(sessionId: string): Promise<void> {
    // Implementation for resuming workflow
    console.log(`Resuming workflow for session: ${sessionId}`);
  }

  async getWorkflowStatus(sessionId: string): Promise<any> {
    // Implementation for getting workflow status
    return { status: 'active', session_id: sessionId };
  }
}
