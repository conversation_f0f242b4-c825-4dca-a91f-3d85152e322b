/**
 * Perplexity API Integration for LangGraph Agent System
 * Implements 12-Factor Agent Principle: Natural Language to Tool Calls
 * Replaces Langbase with Perplexity API
 */

import OpenAI from 'openai';
import { getDatabaseManager } from '../../database/config';
import { z } from 'zod';

// ============================================================================
// PERPLEXITY CLIENT CONFIGURATION
// ============================================================================

export class PerplexityIntegration {
  private client: OpenAI;
  private dbManager = getDatabaseManager();
  
  // Available Perplexity models
  private readonly MODELS = {
    SONAR_PRO: 'sonar-pro',
    SONAR_ONLINE: 'sonar-online',
    LLAMA_LARGE: 'llama-3.1-sonar-large-128k-online',
    LLAMA_SMALL: 'llama-3.1-sonar-small-128k-online'
  };

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.PERPLEXITY_API_KEY,
      baseURL: 'https://api.perplexity.ai'
    });
  }

  // ========================================================================
  // CORE GENERATION METHODS
  // ========================================================================

  async generateResponse(
    messages: Array<{ role: string; content: string }>,
    options: {
      model?: string;
      temperature?: number;
      max_tokens?: number;
      stream?: boolean;
      tools?: any[];
      tool_choice?: string;
    } = {}
  ): Promise<string> {
    try {
      const {
        model = this.MODELS.SONAR_PRO,
        temperature = 0.2,
        max_tokens = 4000,
        stream = false,
        tools,
        tool_choice
      } = options;

      const requestConfig: any = {
        model,
        messages,
        temperature,
        max_tokens,
        stream
      };

      // Add tools if provided
      if (tools && tools.length > 0) {
        requestConfig.tools = tools;
        if (tool_choice) {
          requestConfig.tool_choice = tool_choice;
        }
      }

      const response = await this.client.chat.completions.create(requestConfig);
      
      if (stream) {
        // Handle streaming response
        return this.handleStreamingResponse(response as any);
      }

      const content = response.choices[0]?.message?.content || '';
      
      // Log usage for monitoring
      await this.logApiUsage(model, response.usage);
      
      return content;
    } catch (error) {
      console.error('Perplexity API error:', error);
      throw new Error(`Perplexity API request failed: ${error.message}`);
    }
  }

  async generateWithTools(
    messages: Array<{ role: string; content: string }>,
    tools: any[],
    options: {
      model?: string;
      temperature?: number;
      max_tokens?: number;
    } = {}
  ): Promise<{
    content: string;
    tool_calls: any[];
    finish_reason: string;
  }> {
    try {
      const {
        model = this.MODELS.SONAR_PRO,
        temperature = 0.2,
        max_tokens = 4000
      } = options;

      const response = await this.client.chat.completions.create({
        model,
        messages,
        tools,
        tool_choice: 'auto',
        temperature,
        max_tokens
      });

      const choice = response.choices[0];
      const message = choice.message;

      return {
        content: message.content || '',
        tool_calls: message.tool_calls || [],
        finish_reason: choice.finish_reason || 'stop'
      };
    } catch (error) {
      console.error('Perplexity tool calling error:', error);
      throw new Error(`Perplexity tool calling failed: ${error.message}`);
    }
  }

  // ========================================================================
  // STRUCTURED OUTPUT GENERATION
  // ========================================================================

  async generateStructuredOutput<T>(
    messages: Array<{ role: string; content: string }>,
    schema: z.ZodSchema<T>,
    options: {
      model?: string;
      temperature?: number;
      max_retries?: number;
    } = {}
  ): Promise<T> {
    const {
      model = this.MODELS.SONAR_PRO,
      temperature = 0.1,
      max_retries = 3
    } = options;

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= max_retries; attempt++) {
      try {
        // Add schema instructions to the last message
        const enhancedMessages = [...messages];
        const lastMessage = enhancedMessages[enhancedMessages.length - 1];
        
        lastMessage.content += `\n\nPlease respond with valid JSON that matches this schema:\n${JSON.stringify(schema._def, null, 2)}\n\nResponse must be valid JSON only, no additional text.`;

        const response = await this.generateResponse(enhancedMessages, {
          model,
          temperature,
          max_tokens: 4000
        });

        // Extract JSON from response
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No JSON found in response');
        }

        const parsed = JSON.parse(jsonMatch[0]);
        const validated = schema.parse(parsed);
        
        return validated;
      } catch (error) {
        lastError = error;
        console.warn(`Structured output attempt ${attempt} failed:`, error.message);
        
        if (attempt === max_retries) {
          break;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }

    throw new Error(`Failed to generate structured output after ${max_retries} attempts. Last error: ${lastError?.message}`);
  }

  // ========================================================================
  // EMBEDDING AND SEMANTIC OPERATIONS
  // ========================================================================

  async embed(text: string): Promise<number[]> {
    try {
      // Note: Perplexity doesn't have a dedicated embedding endpoint
      // We'll use a workaround or integrate with OpenAI embeddings
      // For now, we'll simulate embeddings or use OpenAI's embedding API
      
      if (process.env.OPENAI_API_KEY) {
        const openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY
        });
        
        const response = await openai.embeddings.create({
          model: 'text-embedding-3-small',
          input: text,
          encoding_format: 'float'
        });
        
        return response.data[0].embedding;
      }
      
      // Fallback: generate pseudo-embeddings (not recommended for production)
      return this.generatePseudoEmbedding(text);
    } catch (error) {
      console.error('Embedding generation error:', error);
      // Return pseudo-embedding as fallback
      return this.generatePseudoEmbedding(text);
    }
  }

  async compressText(text: string, compressionRatio: number = 0.5): Promise<string> {
    try {
      const targetLength = Math.floor(text.length * compressionRatio);
      
      const response = await this.generateResponse([
        {
          role: 'system',
          content: 'You are an expert text compressor. Compress the given text while preserving all key information, insights, and important details. Maintain the original meaning and context.'
        },
        {
          role: 'user',
          content: `Compress this text to approximately ${targetLength} characters while preserving all important information:\n\n${text}`
        }
      ], {
        temperature: 0.1,
        max_tokens: Math.ceil(targetLength / 3) // Rough token estimation
      });

      return response.trim();
    } catch (error) {
      console.error('Text compression error:', error);
      // Fallback: simple truncation
      return text.substring(0, Math.floor(text.length * compressionRatio)) + '...';
    }
  }

  // ========================================================================
  // TOOL CALLING UTILITIES
  // ========================================================================

  createTool(
    name: string,
    description: string,
    parameters: any
  ): any {
    return {
      type: 'function',
      function: {
        name,
        description,
        parameters
      }
    };
  }

  async executeToolCall(
    toolCall: any,
    availableTools: Map<string, Function>
  ): Promise<any> {
    try {
      const { name, arguments: args } = toolCall.function;
      const toolFunction = availableTools.get(name);
      
      if (!toolFunction) {
        throw new Error(`Tool '${name}' not found`);
      }

      const parsedArgs = typeof args === 'string' ? JSON.parse(args) : args;
      const result = await toolFunction(parsedArgs);
      
      return {
        tool_call_id: toolCall.id,
        role: 'tool',
        name,
        content: JSON.stringify(result)
      };
    } catch (error) {
      console.error(`Tool execution error for ${toolCall.function.name}:`, error);
      return {
        tool_call_id: toolCall.id,
        role: 'tool',
        name: toolCall.function.name,
        content: JSON.stringify({ error: error.message })
      };
    }
  }

  // ========================================================================
  // PROMPT MANAGEMENT
  // ========================================================================

  async loadPromptTemplate(templateName: string, version: string = 'latest'): Promise<string> {
    try {
      const cacheKey = `prompt_template:${templateName}:${version}`;
      const cached = await this.dbManager.getCache<string>(cacheKey);
      
      if (cached) {
        return cached;
      }

      const query = version === 'latest' 
        ? 'SELECT content FROM prompt_templates WHERE template_name = $1 AND is_active = true ORDER BY created_at DESC LIMIT 1'
        : 'SELECT content FROM prompt_templates WHERE template_name = $1 AND version = $2 AND is_active = true LIMIT 1';
      
      const params = version === 'latest' ? [templateName] : [templateName, version];
      const result = await this.dbManager.query(query, params);
      
      if (result.rows.length === 0) {
        throw new Error(`Prompt template '${templateName}' version '${version}' not found`);
      }

      const content = result.rows[0].content;
      
      // Cache for 1 hour
      await this.dbManager.setCache(cacheKey, content, 3600);
      
      return content;
    } catch (error) {
      console.error('Error loading prompt template:', error);
      throw error;
    }
  }

  renderPromptTemplate(template: string, variables: Record<string, any>): string {
    let rendered = template;
    
    // Simple template variable replacement
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      const replacement = typeof value === 'string' ? value : JSON.stringify(value);
      rendered = rendered.replace(new RegExp(placeholder, 'g'), replacement);
    }
    
    return rendered;
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  private async handleStreamingResponse(stream: any): Promise<string> {
    let content = '';
    
    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta?.content;
      if (delta) {
        content += delta;
      }
    }
    
    return content;
  }

  private generatePseudoEmbedding(text: string): number[] {
    // Generate a pseudo-embedding based on text characteristics
    // This is NOT suitable for production use - use proper embeddings
    const embedding = new Array(1536).fill(0);
    
    for (let i = 0; i < text.length && i < 1536; i++) {
      embedding[i] = (text.charCodeAt(i) % 256) / 255.0 - 0.5;
    }
    
    // Normalize the vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  private async logApiUsage(model: string, usage: any): Promise<void> {
    try {
      if (!usage) return;
      
      await this.dbManager.query(`
        INSERT INTO tool_executions 
        (session_id, agent_id, tool_name, input_data, output_data, status, metadata)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        'system',
        'perplexity_api',
        'api_call',
        { model },
        { usage },
        'completed',
        { timestamp: new Date().toISOString() }
      ]);
    } catch (error) {
      console.error('Error logging API usage:', error);
    }
  }

  // ========================================================================
  // HEALTH CHECK AND MONITORING
  // ========================================================================

  async healthCheck(): Promise<{ status: string; latency: number; model: string }> {
    const startTime = Date.now();
    
    try {
      await this.generateResponse([
        { role: 'user', content: 'Hello, this is a health check. Please respond with "OK".' }
      ], {
        model: this.MODELS.SONAR_PRO,
        max_tokens: 10,
        temperature: 0
      });
      
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        latency,
        model: this.MODELS.SONAR_PRO
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
        model: this.MODELS.SONAR_PRO
      };
    }
  }
}

// ============================================================================
// PERPLEXITY EMBEDDER CLASS
// ============================================================================

export class PerplexityEmbedder {
  private integration: PerplexityIntegration;

  constructor() {
    this.integration = new PerplexityIntegration();
  }

  async embed(text: string): Promise<number[]> {
    return this.integration.embed(text);
  }

  async compressText(text: string, ratio: number = 0.5): Promise<string> {
    return this.integration.compressText(text, ratio);
  }
}

// Export singleton instance
export const perplexityIntegration = new PerplexityIntegration();
