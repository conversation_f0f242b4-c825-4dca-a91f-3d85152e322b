"use client";

import React, { use<PERSON>emo, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Grid2X2, List, Mail, Phone, MapPin, Shield, Download, Filter, Search } from "lucide-react";

// Types inferred from rfq_state.suppliers seen in Agent.tsx with extensions for capabilities/certifications
export type Supplier = {
  supplier_id?: string;
  name?: string;
  status?: string; // invited | active | suspended | responded | declined | no_response
  score?: number; // 0-10
  risk_level?: "low" | "medium" | "high" | string;
  contact_info?: { email?: string; phone?: string; address?: string };
  capabilities?: string[];
  certifications?: string[];
};

type Props = {
  suppliers: Supplier[];
  defaultView?: "grid" | "list";
};

const riskColor = (risk?: string) => {
  switch ((risk || "").toLowerCase()) {
    case "low":
      return "text-green-600";
    case "medium":
      return "text-amber-600";
    case "high":
      return "text-red-600";
    default:
      return "text-muted-foreground";
  }
};

const statusBadgeVariant = (status?: string) => {
  const s = (status || "").toLowerCase();
  if (s === "active" || s === "responded") return "default" as const;
  if (s === "invited") return "secondary" as const;
  if (s === "suspended" || s === "declined") return "destructive" as const;
  return "outline" as const;
};

export function VendorDashboard({ suppliers, defaultView = "grid" }: Props) {
  const [view, setView] = useState<"grid" | "list">(defaultView);
  const [q, setQ] = useState("");
  const [risk, setRisk] = useState("all");
  const [status, setStatus] = useState("all");
  const [cert, setCert] = useState("all");
  const [sort, setSort] = useState("score_desc");

  const certifications = useMemo(() => {
    const set = new Set<string>();
    suppliers?.forEach((s) => s.certifications?.forEach((c) => c && set.add(c)));
    return Array.from(set).sort();
  }, [suppliers]);

  const statuses = useMemo(() => {
    const set = new Set<string>();
    suppliers?.forEach((s) => s.status && set.add(s.status));
    return Array.from(set).sort();
  }, [suppliers]);

  const filtered = useMemo(() => {
    let list = [...(suppliers || [])];
    if (q.trim()) {
      const t = q.toLowerCase();
      list = list.filter((s) =>
        (s.name || "").toLowerCase().includes(t) ||
        (s.supplier_id || "").toLowerCase().includes(t)
      );
    }
    if (risk !== "all") list = list.filter((s) => (s.risk_level || "").toLowerCase() === risk);
    if (status !== "all") list = list.filter((s) => (s.status || "").toLowerCase() === status);
    if (cert !== "all") list = list.filter((s) => (s.certifications || []).map((c) => c.toLowerCase()).includes(cert));

    const riskRank = (r?: string) => (r?.toLowerCase() === "high" ? 3 : r?.toLowerCase() === "medium" ? 2 : r?.toLowerCase() === "low" ? 1 : 0);

    list.sort((a, b) => {
      switch (sort) {
        case "score_asc":
          return (a.score || 0) - (b.score || 0);
        case "risk":
          return riskRank(b.risk_level) - riskRank(a.risk_level);
        case "name":
          return (a.name || "").localeCompare(b.name || "");
        case "score_desc":
        default:
          return (b.score || 0) - (a.score || 0);
      }
    });
    return list;
  }, [suppliers, q, risk, status, cert, sort]);

  function exportCSV() {
    const headers = ["Supplier ID", "Name", "Status", "Score", "Risk", "Email", "Phone", "Address", "Capabilities", "Certifications"];
    const rows = filtered.map((s) => [
      s.supplier_id || "",
      s.name || "",
      s.status || "",
      (s.score ?? "").toString(),
      s.risk_level || "",
      s.contact_info?.email || "",
      s.contact_info?.phone || "",
      s.contact_info?.address || "",
      (s.capabilities || []).join("; "),
      (s.certifications || []).join("; "),
    ]);
    const csv = [headers, ...rows].map((r) => r.map((v) => `"${String(v).replaceAll('"', '""')}"`).join(",")).join("\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `suppliers_${new Date().toISOString()}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }

  function exportPDFLike() {
    // Minimal print-to-PDF approach using a new window
    const win = window.open("", "_blank");
    if (!win) return;
    const rows = filtered
      .map(
        (s) =>
          `<tr><td>${s.supplier_id || ""}</td><td>${s.name || ""}</td><td>${s.status || ""}</td><td>${s.score ?? ""}</td><td>${s.risk_level || ""}</td><td>${s.contact_info?.email || ""}</td></tr>`
      )
      .join("");
    win.document.write(`<!doctype html><html><head><title>Suppliers Export</title>
      <style>body{font-family:ui-sans-serif,system-ui,Segoe UI,Roboto,Arial; padding:16px;} table{border-collapse:collapse;width:100%} th,td{border:1px solid #ddd;padding:8px;font-size:12px;} th{background:#f5f5f5;text-align:left}</style>
      </head><body>
      <h2>Suppliers</h2>
      <table><thead><tr><th>ID</th><th>Name</th><th>Status</th><th>Score</th><th>Risk</th><th>Email</th></tr></thead><tbody>${rows}</tbody></table>
      <script>window.print();</script>
      </body></html>`);
    win.document.close();
  }

  return (
    <div className="space-y-4">
      <Card className="bg-card border-border">
        <CardHeader>
          <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
            <CardTitle className="text-lg">Vendors</CardTitle>
            <div className="flex flex-col gap-2 md:flex-row md:items-center">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input value={q} onChange={(e) => setQ(e.target.value)} placeholder="Search by name or ID" className="pl-8" />
              </div>
              <Select value={sort} onValueChange={setSort}>
                <SelectTrigger className="w-[150px]"><SelectValue placeholder="Sort" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="score_desc">Score: High → Low</SelectItem>
                  <SelectItem value="score_asc">Score: Low → High</SelectItem>
                  <SelectItem value="risk">Risk: High → Low</SelectItem>
                  <SelectItem value="name">Name A → Z</SelectItem>
                </SelectContent>
              </Select>
              <Select value={risk} onValueChange={setRisk}>
                <SelectTrigger className="w-[140px]"><SelectValue placeholder="Risk" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All risks</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="w-[160px]"><SelectValue placeholder="Status" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  {statuses.map((s) => (
                    <SelectItem key={s} value={s.toLowerCase()}>{s}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={cert} onValueChange={setCert}>
                <SelectTrigger className="w-[180px]"><SelectValue placeholder="Certification" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All certifications</SelectItem>
                  {certifications.map((c) => (
                    <SelectItem key={c} value={c.toLowerCase()}>{c}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex items-center gap-1">
                <Button variant={view === "grid" ? "default" : "outline"} size="icon" onClick={() => setView("grid")} aria-label="Grid view">
                  <Grid2X2 className="h-4 w-4" />
                </Button>
                <Button variant={view === "list" ? "default" : "outline"} size="icon" onClick={() => setView("list")} aria-label="List view">
                  <List className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={exportCSV}>
                  <Download className="mr-1 h-4 w-4" /> CSV
                </Button>
                <Button variant="outline" size="sm" onClick={exportPDFLike}>
                  <Filter className="mr-1 h-4 w-4" /> PDF
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {view === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filtered.map((s, idx) => (
                <VendorCard key={(s.supplier_id || s.name || idx).toString()} supplier={s} />
              ))}
            </div>
          ) : (
            <ScrollArea className="h-auto max-h-[520px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Risk</TableHead>
                    <TableHead>Certifications</TableHead>
                    <TableHead>Contact</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filtered.map((s, idx) => (
                    <VendorRow key={(s.supplier_id || s.name || idx).toString()} supplier={s} />
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          )}
          {filtered.length === 0 && (
            <p className="text-sm text-muted-foreground">No suppliers match your criteria.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function VendorCard({ supplier }: { supplier: Supplier }) {
  const scorePct = Math.min(100, Math.max(0, (supplier.score ?? 0) * 10));
  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-start justify-between mb-2">
        <div>
          <h4 className="font-medium leading-tight">{supplier.name || "Unknown Supplier"}</h4>
          {supplier.supplier_id && (
            <p className="text-xs text-muted-foreground">ID: {supplier.supplier_id}</p>
          )}
        </div>
        <Badge variant={statusBadgeVariant(supplier.status)}>{supplier.status || "pending"}</Badge>
      </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="text-sm">Score</span>
          <span className="text-sm font-medium">{supplier.score ?? "-"}/10</span>
        </div>
        <Progress value={scorePct} />
        <div className="flex items-center gap-2 text-sm">
          <Shield className="h-4 w-4" />
          <span className={cn("capitalize", riskColor(supplier.risk_level))}>{supplier.risk_level || "unknown"} risk</span>
        </div>

        <HoverCard>
          <HoverCardTrigger asChild>
            <Button variant="ghost" size="sm" className="px-0 justify-start text-sm">
              Contact details
            </Button>
          </HoverCardTrigger>
          <HoverCardContent className="w-64">
            <div className="space-y-2 text-sm">
              {supplier.contact_info?.email && (
                <div className="flex items-center gap-2"><Mail className="h-4 w-4" /> {supplier.contact_info.email}</div>
              )}
              {supplier.contact_info?.phone && (
                <div className="flex items-center gap-2"><Phone className="h-4 w-4" /> {supplier.contact_info.phone}</div>
              )}
              {supplier.contact_info?.address && (
                <div className="flex items-start gap-2"><MapPin className="h-4 w-4 mt-0.5" /> <span>{supplier.contact_info.address}</span></div>
              )}
              {!supplier.contact_info?.email && !supplier.contact_info?.phone && !supplier.contact_info?.address && (
                <div className="text-muted-foreground">No contact information available.</div>
              )}
            </div>
          </HoverCardContent>
        </HoverCard>

        <Collapsible>
          <CollapsibleTrigger asChild>
            <Button variant="outline" size="sm" className="w-full">Capabilities</Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-2">
            <div className="flex flex-wrap gap-1">
              {(supplier.capabilities || []).map((c, i) => (
                <Badge key={i} variant="outline" className="text-xs">{c}</Badge>
              ))}
              {(supplier.capabilities || []).length === 0 && (
                <p className="text-xs text-muted-foreground">No capabilities listed.</p>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Collapsible>
          <CollapsibleTrigger asChild>
            <Button variant="outline" size="sm" className="w-full">Certifications</Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-2">
            <div className="flex flex-wrap gap-1">
              {(supplier.certifications || []).map((c, i) => (
                <Badge key={i} variant="secondary" className="text-xs">{c}</Badge>
              ))}
              {(supplier.certifications || []).length === 0 && (
                <p className="text-xs text-muted-foreground">No certifications listed.</p>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}

function VendorRow({ supplier }: { supplier: Supplier }) {
  const scorePct = Math.min(100, Math.max(0, (supplier.score ?? 0) * 10));
  return (
    <TableRow>
      <TableCell className="max-w-[240px]">
        <div className="flex flex-col">
          <span className="font-medium leading-tight">{supplier.name || "Unknown Supplier"}</span>
          {supplier.supplier_id && (
            <span className="text-xs text-muted-foreground">ID: {supplier.supplier_id}</span>
          )}
        </div>
      </TableCell>
      <TableCell>
        <Badge variant={statusBadgeVariant(supplier.status)}>{supplier.status || "pending"}</Badge>
      </TableCell>
      <TableCell className="w-[220px]">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{supplier.score ?? "-"}/10</span>
        </div>
        <Progress value={scorePct} />
      </TableCell>
      <TableCell>
        <span className={cn("capitalize", riskColor(supplier.risk_level))}>{supplier.risk_level || "unknown"}</span>
      </TableCell>
      <TableCell>
        <div className="flex flex-wrap gap-1 max-w-[260px]">
          {(supplier.certifications || []).slice(0, 4).map((c, i) => (
            <Badge key={i} variant="secondary" className="text-xs">{c}</Badge>
          ))}
          {(supplier.certifications || []).length > 4 && (
            <Badge variant="outline" className="text-xs">+{(supplier.certifications || []).length - 4}</Badge>
          )}
          {(supplier.certifications || []).length === 0 && (
            <span className="text-xs text-muted-foreground">—</span>
          )}
        </div>
      </TableCell>
      <TableCell>
        <HoverCard>
          <HoverCardTrigger asChild>
            <Button variant="ghost" size="sm" className="px-2">View</Button>
          </HoverCardTrigger>
          <HoverCardContent className="w-64">
            <div className="space-y-2 text-sm">
              {supplier.contact_info?.email && (
                <div className="flex items-center gap-2"><Mail className="h-4 w-4" /> {supplier.contact_info.email}</div>
              )}
              {supplier.contact_info?.phone && (
                <div className="flex items-center gap-2"><Phone className="h-4 w-4" /> {supplier.contact_info.phone}</div>
              )}
              {supplier.contact_info?.address && (
                <div className="flex items-start gap-2"><MapPin className="h-4 w-4 mt-0.5" /> <span>{supplier.contact_info.address}</span></div>
              )}
              {!supplier.contact_info?.email && !supplier.contact_info?.phone && !supplier.contact_info?.address && (
                <div className="text-muted-foreground">No contact information available.</div>
              )}
            </div>
          </HoverCardContent>
        </HoverCard>
      </TableCell>
    </TableRow>
  );
}

